services:
  production-database:
    image: mongo:5.0.21
    container_name: production-database
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: production
    ports:
      - "27017:27017"
    volumes:
      - production-db-data:/data/db

  darkroom-production-database:
    image: mongo:5.0.21
    container_name: darkroom-production-database
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: darkroom
    ports:
      - "27018:27017"
    volumes:
      - darkroom-db-data:/data/db
    healthcheck:
      test: ["CMD", "mongo", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 30s

  app-builder:
    build:
      context: ..
      dockerfile: Dockerfile
      target: development2
    image: bizclik-app:latest
    command: ["sh", "-c", "yarn install && yarn build-all"]
    volumes:
      - app:/app
      - app-dist:/app/dist
      - app-node_modules:/app/node_modules

  admin-production:
    build:
      context: ..
      dockerfile: Dockerfile
      target: development2
    image: bizclik-app:latest
    command: ["node", "/app/admin/app.js"]
    environment:
      NODE_ENV: production
      MONGO_SRV: ******************************************************************************
      MONGO_URL: ******************************************************************************
      DATABASE: production
      HOST: production-database
      USERNAME: admin
      PASSWORD: password
    ports:
      - "7001:7001"
    volumes:
      - app:/app
      - app-dist:/app/dist
      - app-node_modules:/app/node_modules
    depends_on:
      production-database:
        condition: service_started
      app-builder:
        condition: service_completed_successfully

  api-production:
    build:
      context: ..
      dockerfile: Dockerfile
      target: development2
    image: bizclik-app:latest
    command: ["node", "/app/dist/api/app.js"]
    environment:
      NODE_ENV: production
      MONGO_SRV: ******************************************************************************
      MONGO_URL: ******************************************************************************
      DATABASE: production
      HOST: production-database
      USERNAME: admin
      PASSWORD: password
    ports:
      - "7002:7002"
    volumes:
      - app:/app
      - app-dist:/app/dist
      - app-node_modules:/app/node_modules
    depends_on:
      production-database:
        condition: service_started
      app-builder:
        condition: service_completed_successfully

  site-production:
    build:
      context: ..
      dockerfile: Dockerfile
      target: development2
    image: bizclik-app:latest
    command: ["node", "/app/dist/site/server/app.js"]
    environment:
      NODE_ENV: production
      MONGO_SRV: ******************************************************************************
      MONGO_URL: ******************************************************************************
      DATABASE: production
      HOST: production-database
      USERNAME: admin
      PASSWORD: password
    ports:
      - "7000:7000"
    volumes:
      - app:/app
      - app-dist:/app/dist
      - app-node_modules:/app/node_modules
    depends_on:
      production-database:
        condition: service_started
      app-builder:
        condition: service_completed_successfully

  message-bus-production:
    build:
      context: ..
      dockerfile: Dockerfile
      target: development2
    image: bizclik-app:latest
    command: ["node", "/app/message-bus/app.js"]
    environment:
      NODE_ENV: production
      MONGO_SRV: ******************************************************************************
      MONGO_URL: ******************************************************************************
      DATABASE: production
      HOST: production-database
      USERNAME: admin
      PASSWORD: password
    volumes:
      - app:/app
      - app-dist:/app/dist
      - app-node_modules:/app/node_modules
    depends_on:
      production-database:
        condition: service_started
      app-builder:
        condition: service_completed_successfully

  worker-production:
    build:
      context: ..
      dockerfile: Dockerfile
      target: development2
    image: bizclik-app:latest
    command: ["node", "/app/dist/worker/app.js"]
    environment:
      NODE_ENV: production
      MONGO_SRV: ******************************************************************************
      MONGO_URL: ******************************************************************************
      DATABASE: production
      HOST: production-database
      USERNAME: admin
      PASSWORD: password
    ports:
      - "8114:8114"
    volumes:
      - app:/app
      - app-dist:/app/dist
      - app-node_modules:/app/node_modules
    depends_on:
      production-database:
        condition: service_started
      app-builder:
        condition: service_completed_successfully

  varnish-production:
    build:
      context: ../infra/varnish
      dockerfile: Dockerfile
      args:
        NODE_ENV: production
    image: varnish:latest
    environment:
      NODE_ENV: production
      VARNISH_SIZE: 100M
      VARNISH_HTTP_PORT: 8080
    ports:
      - "8080:8080"
    depends_on:
      - site-production
      - api-production
      - admin-production

  darkroom-production:
    build:
      context: https://github.com/clocklimited/Darkroom-api.git
      dockerfile: Dockerfile
    image: darkroom:latest
    environment:
      NODE_ENV: production
      DATABASE_URI: *************************************************************************************
      SALT: 53370bcf28896ade6f6d810d528ef3d4
      KEY: 717d5fe4fcf607b3ee4f7287cf6cdfec
    ports:
      - "17999:17999"
    depends_on:
      darkroom-production-database:
        condition: service_healthy

volumes:
  production-db-data:
  darkroom-db-data:
  app-dist:
  app:
  app-node_modules:
