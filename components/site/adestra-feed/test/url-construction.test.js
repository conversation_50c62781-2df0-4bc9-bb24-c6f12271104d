const assert = require('assert')

describe('Adestra Feed URL Construction Logic', function () {
  it('should use __fullUrlPath when available for regular articles', function () {
    const baseUrl = 'https://example.com'
    const article = {
      _id: '123',
      slug: 'test-article',
      contentType: 'Article',
      __fullUrlPath: '/technology/test-article'
    }

    // Simulate the URL construction logic from the controller
    let link = baseUrl
    switch (article.contentType) {
      case 'Company Report':
        link += article.__fullUrlPath || `/company-reports/${article.slug}`
        break
      case 'Whitepaper':
        link += article.__fullUrlPath || `/article/${article.slug}`
        break
      case 'Magazine':
        link += article.__fullUrlPath || `/magazine/${article.slug}`
        break
      default:
        link += article.__fullUrlPath || `/article/${article.slug}`
    }

    assert.strictEqual(link, 'https://example.com/technology/test-article')
  })

  it('should fall back to /article/slug when __fullUrlPath is missing for regular articles', function () {
    const baseUrl = 'https://example.com'
    const article = {
      _id: '123',
      slug: 'test-article',
      contentType: 'Article',
      __fullUrlPath: null
    }

    let link = baseUrl
    switch (article.contentType) {
      case 'Company Report':
        link += article.__fullUrlPath || `/company-reports/${article.slug}`
        break
      case 'Whitepaper':
        link += article.__fullUrlPath || `/article/${article.slug}`
        break
      case 'Magazine':
        link += article.__fullUrlPath || `/magazine/${article.slug}`
        break
      default:
        link += article.__fullUrlPath || `/article/${article.slug}`
    }

    assert.strictEqual(link, 'https://example.com/article/test-article')
  })

  it('should use __fullUrlPath for Company Reports when available', function () {
    const baseUrl = 'https://example.com'
    const article = {
      _id: '123',
      slug: 'test-report',
      contentType: 'Company Report',
      __fullUrlPath: '/reports/companies/test-report'
    }

    let link = baseUrl
    switch (article.contentType) {
      case 'Company Report':
        link += article.__fullUrlPath || `/company-reports/${article.slug}`
        break
      case 'Whitepaper':
        link += article.__fullUrlPath || `/article/${article.slug}`
        break
      case 'Magazine':
        link += article.__fullUrlPath || `/magazine/${article.slug}`
        break
      default:
        link += article.__fullUrlPath || `/article/${article.slug}`
    }

    assert.strictEqual(link, 'https://example.com/reports/companies/test-report')
  })

  it('should fall back to /company-reports/slug for Company Reports when __fullUrlPath is missing', function () {
    const baseUrl = 'https://example.com'
    const article = {
      _id: '123',
      slug: 'test-report',
      contentType: 'Company Report',
      __fullUrlPath: null
    }

    let link = baseUrl
    switch (article.contentType) {
      case 'Company Report':
        link += article.__fullUrlPath || `/company-reports/${article.slug}`
        break
      case 'Whitepaper':
        link += article.__fullUrlPath || `/article/${article.slug}`
        break
      case 'Magazine':
        link += article.__fullUrlPath || `/magazine/${article.slug}`
        break
      default:
        link += article.__fullUrlPath || `/article/${article.slug}`
    }

    assert.strictEqual(link, 'https://example.com/company-reports/test-report')
  })

  it('should use __fullUrlPath for Whitepapers when available', function () {
    const baseUrl = 'https://example.com'
    const article = {
      _id: '123',
      slug: 'test-whitepaper',
      contentType: 'Whitepaper',
      __fullUrlPath: '/resources/whitepapers/test-whitepaper'
    }

    let link = baseUrl
    switch (article.contentType) {
      case 'Company Report':
        link += article.__fullUrlPath || `/company-reports/${article.slug}`
        break
      case 'Whitepaper':
        link += article.__fullUrlPath || `/article/${article.slug}`
        break
      case 'Magazine':
        link += article.__fullUrlPath || `/magazine/${article.slug}`
        break
      default:
        link += article.__fullUrlPath || `/article/${article.slug}`
    }

    assert.strictEqual(link, 'https://example.com/resources/whitepapers/test-whitepaper')
  })

  it('should fall back to /article/slug for Whitepapers when __fullUrlPath is missing', function () {
    const baseUrl = 'https://example.com'
    const article = {
      _id: '123',
      slug: 'test-whitepaper',
      contentType: 'Whitepaper',
      __fullUrlPath: null
    }

    let link = baseUrl
    switch (article.contentType) {
      case 'Company Report':
        link += article.__fullUrlPath || `/company-reports/${article.slug}`
        break
      case 'Whitepaper':
        link += article.__fullUrlPath || `/article/${article.slug}`
        break
      case 'Magazine':
        link += article.__fullUrlPath || `/magazine/${article.slug}`
        break
      default:
        link += article.__fullUrlPath || `/article/${article.slug}`
    }

    assert.strictEqual(link, 'https://example.com/article/test-whitepaper')
  })
})
