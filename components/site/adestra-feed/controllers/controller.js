/**
 * Adestra Feed Controller
 *
 * This controller creates an RSS feed specifically formatted for Adestra email marketing platform.
 * It retrieves articles from the database based on the current instance and account context,
 * and formats them into a simplified RSS XML format that Adestra can consume.
 *
 * Local Environment Context:
 * - The feed is accessible at /api/adestra-feed
 * - It uses the instance and account objects from the request (populated by middleware)
 * - It queries the articleService and authorService from the serviceLocator
 * - Images are served from the darkroom service (configured in config.json)
 * - The feed is instance-specific (filtered by req.instance._id)
 */

// Dependencies
const { promisify } = require('util') // Converts callback-based methods to promises
const moment = require('moment') // Used for date calculations
const createImageUrlBuilder = require('cf-image-url-builder') // For image URL generation

// Custom utilities
const createInstanceFilteredServiceRetriever = require('../../../service/section/lib/instance-filtered-service-retriever')
const articleFilter = require('../../../service/article/filter')

// Import the plain text getter utility
const { getPlaintext } = require('../../../api/article/lib/plain-text-getter')

/**
 * Creates and configures the Adestra feed controller
 *
 * @param {Object} serviceLocator - Service locator containing all required services
 * @returns {Function} Controller setup function
 */
const createController = (serviceLocator) => {
  // Get logger for debugging
  const logger = serviceLocator.logger || console

  // Creates a method that respects instance and account filtering logic
  const retrieveInstanceFilteredServices = createInstanceFilteredServiceRetriever(
    serviceLocator
  )

  // Helper function to find the optimal image for an article
  const findOptimalImage = (article) => {
    if (!article.images) return ''

    // Target specifically the 668-wide image
    // First priority: hero_landscape_668 (exactly what we want)
    if (
      article.images.hero_landscape_668?.length > 0 &&
      article.images.hero_landscape_668[0]?.url
    ) {
      return article.images.hero_landscape_668[0].url
    }

    // Second priority: any key with "668" and "landscape"
    for (const key in article.images) {
      if (
        key.includes('668') &&
        key.includes('landscape') &&
        article.images[key]?.length > 0 &&
        article.images[key][0]?.url
      ) {
        return article.images[key][0].url
      }
    }

    // Third priority: any key with "668" (maintaining width)
    for (const key in article.images) {
      if (
        key.includes('668') &&
        article.images[key]?.length > 0 &&
        article.images[key][0]?.url
      ) {
        return article.images[key][0].url
      }
    }

    // Fallback to other images if no 668-wide image is found
    // Fourth priority: any landscape image
    for (const key in article.images) {
      if (
        key.includes('landscape') &&
        article.images[key]?.length > 0 &&
        article.images[key][0]?.url
      ) {
        return article.images[key][0].url
      }
    }

    // Last resort: any image
    for (const key in article.images) {
      if (article.images[key]?.length > 0 && article.images[key][0]?.url) {
        return article.images[key][0].url
      }
    }

    return ''
  }

  // Helper function to process image URL for email compatibility
  const processImageUrl = (url) => {
    if (!url) return ''

    // Always ensure we have a .jpg extension
    if (url.includes('.webp')) {
      url = url.replace('.webp', '.jpg')
    } else if (!url.endsWith('.jpg')) {
      // If URL doesn't already end with .jpg, add it
      url = `${url}.jpg`
    }

    return url
  }

  /**
   * Route: /api/adestra-feed
   *
   * Query parameters:
   * - limit: Maximum number of articles to include (default: 10)
   * - days: Number of days to look back for articles (default: 30)
   * - contentType: Type of content to include (currently only 'article' is supported)
   *
   * Response:
   * - Content-Type: application/rss+xml
   * - RSS 2.0 format XML with <item> entries for each article
   */
  serviceLocator.router.get('/api/adestra-feed', async (req, res) => {
    try {
      // Log request for debugging
      logger.info(
        `Adestra feed requested for instance: ${req.instance.name} (${req.instance._id})`
      )

      const config = req.query
      logger.debug('Adestra feed config:', config)

      // Get article filter specific to this instance/account context
      const { applyArticleFilter } = await retrieveInstanceFilteredServices(
        req.instance._id,
        req.account._id
      )

      // Build the Mongo query for articles published in the past X days (default: 30)
      const days = parseInt(config.days || '30')
      const articleQuery = {
        ...articleFilter.publicQuery(applyArticleFilter({})),
        displayDate: {
          $gte: moment().subtract(days, 'days').toDate()
        }
      }

      // Add content type filter if specified
      if (config.contentType) {
        articleQuery.contentType = config.contentType
      }

      const articleOptions = {
        sort: { displayDate: -1 },
        limit: parseInt(config.limit || '50')
      }

      logger.debug('Adestra feed article query:', articleQuery)

      // Set projection to include the full URL path
      const articleProjection = {
        headline: 1,
        sell: 1,
        displayDate: 1,
        __fullUrlPath: 1,
        images: 1,
        author: 1,
        legacyAuthorName: 1,
        featured: 1,
        body: 1,
        contentType: 1, // Add content type
        slug: 1 // Add slug
      }

      // Query the articles from the service
      const articles = await promisify(
        serviceLocator.articleService.findPublic
      )(articleQuery, { ...articleOptions, projection: articleProjection })

      logger.debug(`Found ${articles.length} articles for Adestra feed`)

      // Verify each article has a valid URL path
      articles.forEach((article) => {
        if (!article.__fullUrlPath) {
          logger.warn(
            `Article ${article._id} (${article.headline}) missing __fullUrlPath`
          )
        }
      })

      // Query the authors (for populating <author> field)
      const authors = await promisify(serviceLocator.authorService.find)(
        { $or: [{ instances: req.instance._id }, { instances: [] }] },
        { projection: { name: 1 } }
      )

      const baseUrl = `${req.protocol}://${req.hostname}`
      // Find the newest featured article (articles are already sorted by displayDate in descending order)
      const newestFeaturedArticle = articles.find(
        (article) => article.featured === true
      )
      const newestFeaturedArticleId = newestFeaturedArticle
        ? newestFeaturedArticle._id.toString()
        : null

      // Initialize arrays to hold regular and featured items
      let regularItems = [];
      let featuredItem = null;

      // Helper function to get truncated body text
      const getTruncatedBodyText = (article) => {
        if (!article.body || !article.body.widgets) return ''

        // Get plain text from article body
        let fullText = getPlaintext(article.body.widgets)

        // Remove hyperlinks in square brackets
        fullText = fullText.replace(/\[[^\]]*\]/g, '')

        // If text is empty after cleaning, return empty string
        if (!fullText.trim()) return ''

        // Find the nearest period to the 500-character mark or end of text
        const targetLength = Math.min(500, fullText.length)

        // Look for periods within a reasonable range before and after target
        const lowerBound = Math.max(0, targetLength - 100)
        const upperBound = Math.min(fullText.length, targetLength + 100)

        let bestCutoffIndex = targetLength // Default if no period found
        let minDistance = 100 // Initialize with maximum possible distance

        // Check each character in our search range
        for (let i = lowerBound; i < upperBound; i++) {
          if (fullText[i] === '.') {
            const distance = Math.abs(i - targetLength)
            if (distance < minDistance) {
              minDistance = distance
              bestCutoffIndex = i
            }
          }
        }

        // Include the period in the truncated text
        bestCutoffIndex++

        // Ensure we don't go beyond the text length
        bestCutoffIndex = Math.min(bestCutoffIndex, fullText.length)

        return fullText.substring(0, bestCutoffIndex).trim()
      }

      // Iterate through each article and build individual <item> blocks
      for (const article of articles) {
        const author = authors.find((a) => a._id === article.author)

        // Properly escape title to handle special characters like &
        const title = (article.headline || 'Untitled')
          .replace(/&/g, '&amp;')
          .replace(/</g, '&lt;')
          .replace(/>/g, '&gt;')

        const description = (article.sell || '')
          .replace(/&/g, '&amp;')
          .replace(/</g, '&lt;')
          .replace(/>/g, '&gt;')

        const pubDate = new Date(article.displayDate).toUTCString()
        
        // Construct the URL properly using the article's full URL path
        let link = `${baseUrl}`;

        // Use the article's computed __fullUrlPath when available, with fallbacks for special content types
        switch (article.contentType) {
          case 'Company Report':
            // Company reports have a specific URL pattern, but prefer __fullUrlPath if available
            link += article.__fullUrlPath || `/company-reports/${article.slug}`;
            break;
          case 'Whitepaper':
            // Whitepapers should use __fullUrlPath, fallback to /article/slug
            link += article.__fullUrlPath || `/article/${article.slug}`;
            break;
          case 'Magazine':
            // Magazine articles have a specific URL pattern, but prefer __fullUrlPath if available
            link += article.__fullUrlPath || `/magazine/${article.slug}`;
            break;
          default:
            // For regular articles, use __fullUrlPath or fallback to /article/slug
            link += article.__fullUrlPath || `/article/${article.slug}`;
        }

        // Log the constructed URL for debugging
        logger.debug(`Article ${article._id} URL: ${link} (contentType: ${article.contentType}, __fullUrlPath: ${article.__fullUrlPath})`)

        // Create guid tag using the link as permalink
        const guidTag = `<guid isPermaLink="true">${link}</guid>`

        // Use the image URL builder like other parts of the codebase
        let imageUrl = ''
        if (article.images && article.images.widgets) {
          const urlBuilder = createImageUrlBuilder(
            serviceLocator.config.darkroom.url,
            serviceLocator.config.darkroom.salt,
            article.images.widgets
          )
          imageUrl = urlBuilder
            .getImage('Hero')
            .crop('Landscape')
            .constrain(668)
            .url()

          // Convert to JPG if needed
          if (imageUrl.includes('.webp')) {
            imageUrl = imageUrl.replace('.webp', '.jpg')
          } else if (!imageUrl.endsWith('.jpg')) {
            imageUrl = `${imageUrl}.jpg`
          }
        } else {
          // Fallback to your existing method
          imageUrl = processImageUrl(findOptimalImage(article))
        }

        // Create image tag if an image was found
        const imageTag = imageUrl
          ? `<enclosure url="${imageUrl}" length="0" type="image/jpg" />`
          : ''

        // Format author name
        const authorNameRaw =
          author?.name || article.legacyAuthorName || 'Unknown'
        const authorName = authorNameRaw
          .replace(/&/g, '&amp;')
          .replace(/</g, '&lt;')
          .replace(/>/g, '&gt;')

        // Check if this is the newest featured article
        const isNewestFeatured =
          newestFeaturedArticleId &&
          article._id.toString() === newestFeaturedArticleId

        // Get truncated body text
        const bodyText = getTruncatedBodyText(article)
          .replace(/&/g, '&amp;')
          .replace(/</g, '&lt;')
          .replace(/>/g, '&gt;')

        // Add body text tag
        const bodyTextTag = bodyText ? `<content>${bodyText}</content>` : ''

        // Build XML for one <item>
        let itemXml = '';

        if (isNewestFeatured) {
          // If this is the newest featured article, store it separately
          featuredItem = `
            <item>
              <featured>
                <title>${title}</title>
                <link>${link}</link>
                <description>${description}</description>
                <pubDate>${pubDate}</pubDate>
                <author>${authorName}</author>
                ${guidTag}
                ${imageTag}
                ${bodyTextTag}
              </featured>
            </item>`;
        } else {
          // If not the newest featured article, add to regular items
          regularItems.push(`
            <item>
              <title>${title}</title>
              <link>${link}</link>
              <description>${description}</description>
              <pubDate>${pubDate}</pubDate>
              <author>${authorName}</author>
              ${guidTag}
              ${imageTag}
              ${bodyTextTag}
            </item>`);
        }
      }

      // Combine featured item first, then add regular items
      let rssItems = '';
      if (featuredItem) {
        rssItems = featuredItem;
      }
      rssItems += regularItems.join('');

      // Create standard RSS 2.0 feed (no namespaces, simple structure, lowercase tags)
      const rssXml = `<?xml version="1.0" encoding="UTF-8"?>
<rss version="2.0">
<channel>
  <title>${req.instance.name} – Articles</title>
  <link>${baseUrl}</link>
  <description>${req.instance.strapline
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')}</description>
  <language>en-us</language>
  <lastbuilddate>${new Date().toUTCString()}</lastbuilddate>
  <ttl>60</ttl>
  ${rssItems}
</channel>
</rss>`

      // Estimate feed size for logging
      const feedSizeBytes = Buffer.byteLength(rssXml, 'utf8')
      const feedSizeMB = (feedSizeBytes / (1024 * 1024)).toFixed(2)

      // Check if feed is within size limits
      const isWithinSizeLimit = feedSizeBytes < 10 * 1024 * 1024 // 10MB limit

      // Response headers for RSS and caching
      res.setHeader('Content-Type', 'application/rss+xml')
      res.setHeader('Cache-Control', 'public, max-age=600') // Allow caching for 10 minutes

      // Add additional headers to help with debugging
      res.setHeader('X-Feed-Size', `${feedSizeMB}MB`)
      res.setHeader('X-Feed-Articles', articles.length)
      res.setHeader('X-Feed-Generated', new Date().toISOString())

      // Send the feed
      res.send(rssXml.trim())

      // Log detailed information about the feed
      logger.info(
        `Adestra feed successfully generated with ${articles.length} articles, size: ${feedSizeMB}MB`
      )

      if (!isWithinSizeLimit) {
        logger.warn(
          `Adestra feed exceeds recommended size limit of 10MB. Current size: ${feedSizeMB}MB`
        )
      }

      // Log the feed URL for easy testing
      logger.info(`Feed URL: ${baseUrl}/api/adestra-feed`)
    } catch (err) {
      // Catch unexpected errors and log for debugging
      logger.error('Adestra feed error:', err)

      // Send more detailed error response
      const errorMessage =
        process.env.NODE_ENV === 'development'
          ? `Error generating Adestra feed: ${err.message}`
          : 'Internal Server Error'

      res.status(500).send(errorMessage)
    }
  })

  return { router: serviceLocator.router }
}

module.exports = createController
